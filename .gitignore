# Security: Environment variables and secrets
.env
.env.*
!.env.example
*.key
*.pem
*.p12
*.pfx
*.token
*.auth
*.secret

# Security: Configuration files with sensitive data
ngrok.yml
config.json
secrets.json
credentials.json

# Application data directories
n8n-data/
logs/
data/
uploads/
temp/
cache/

# Binary and executable files
ngrok.exe
*.exe
*.zip
*.tar.gz
*.dmg
*.pkg

# Database files
*.sqlite
*.sqlite3
*.db
*.mdb

# Log files
*.log
logs/
*.log.*

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker data directories (exclude)
docker-data/
volumes/
docker-volumes/

# Docker configuration files (include these - they should be committed)
# .dockerignore - SHOULD BE COMMITTED
# docker-compose.yml - SHOULD BE COMMITTED
# docker-compose.*.yml - SHOULD BE COMMITTED
# Dockerfile* - SHOULD BE COMMITTED

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Security: Backup and temporary files
*.bak
*.backup
*.tmp
*.temp
*~
.#*

# Security: Certificate and key files
*.crt
*.cer
*.der
*.p7b
*.p7c
*.p7r
*.spc
*.csr

# Security: SSH keys
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.ppk

# Security: GPG keys
*.gpg
*.asc

# Testing and coverage
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*

# Documentation builds
docs/_build/
.readthedocs.yml

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json