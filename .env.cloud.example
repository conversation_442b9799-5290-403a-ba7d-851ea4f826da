# Instagram Automation - Cloud Deployment Configuration
# Copy this file to .env.cloud and fill in your actual values

# =============================================================================
# NGROK CONFIGURATION (Free Account)
# =============================================================================
# Get your free authtoken from: https://dashboard.ngrok.com/get-started/your-authtoken
NGROK_AUTHTOKEN=your_ngrok_authtoken_here

# Free ngrok subdomains (optional, will use random if not specified)
NGROK_N8N_SUBDOMAIN=your-n8n-subdomain
NGROK_API_SUBDOMAIN=your-api-subdomain

# =============================================================================
# N8N CONFIGURATION
# =============================================================================
# Basic Authentication (REQUIRED for cloud deployment)
N8N_BASIC_AUTH_USER=your_username
N8N_BASIC_AUTH_PASSWORD=your_secure_password

# External URLs (will be set automatically by ngrok)
N8N_EDITOR_BASE_URL=https://your-n8n-subdomain.ngrok-free.app
WEBHOOK_URL=https://your-n8n-subdomain.ngrok-free.app

# Protocol and Host
N8N_PROTOCOL=https
N8N_HOST=0.0.0.0

# Logging
N8N_LOG_LEVEL=info
N8N_LOG_OUTPUT=console

# =============================================================================
# AI API KEYS (Add when ready for production)
# =============================================================================
# Claude AI (Anthropic)
CLAUDE_API_KEY=your_claude_api_key_here

# OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# INSTAGRAM API (Meta Developer)
# =============================================================================
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment mode
FLASK_ENV=production
FLASK_DEBUG=false

# Mock mode (set to false when using real APIs)
MOCK_MODE=true

# MCP Configuration
MCP_BASE_URL=http://mock-api:8000

# =============================================================================
# CLOUD PROVIDER SPECIFIC (Optional)
# =============================================================================
# For AWS, GCP, Azure, DigitalOcean, etc.
CLOUD_PROVIDER=aws
CLOUD_REGION=us-east-1

# Database (if using external database)
DATABASE_URL=postgresql://user:password@host:port/database

# Redis (if using external Redis)
REDIS_URL=redis://localhost:6379

# =============================================================================
# MONITORING & ALERTS (Optional)
# =============================================================================
# Webhook for status notifications
WEBHOOK_NOTIFICATION_URL=https://hooks.slack.com/your/webhook/url

# Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# JWT Secret for API authentication
JWT_SECRET=your_jwt_secret_here

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# =============================================================================
# BACKUP CONFIGURATION (Optional)
# =============================================================================
# S3 Bucket for backups
S3_BUCKET_NAME=your-backup-bucket
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
S3_REGION=us-east-1

# Backup schedule (cron format)
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# =============================================================================
# DEVELOPMENT/TESTING OVERRIDES
# =============================================================================
# Override for development
DEV_MODE=false
DEBUG_WEBHOOKS=false
SKIP_SSL_VERIFY=false
