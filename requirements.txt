# Security-hardened dependencies with pinned versions
# Last updated: 2024-12-28 - Latest security patches

# AI API clients with security patches
anthropic==0.52.1
openai==1.82.0

# Environment and configuration
python-dotenv==1.1.0

# Web framework with security patches
flask==3.1.1
flask-cors==6.0.0

# HTTP client with security patches
httpx==0.28.1

# Async support (built into Python 3.11+)
# asyncio is part of standard library

# Security: Additional dependencies for production
gunicorn==23.0.0  # Production WSGI server
cryptography==45.0.3  # Secure cryptographic operations
certifi==2025.4.26  # Updated CA certificates
urllib3==2.4.0  # Secure HTTP library
requests==2.32.3  # HTTP library with security patches

# Security: Input validation and sanitization
bleach==6.2.0  # HTML sanitization
validators==0.35.0  # Input validation

# Security: Rate limiting and protection
flask-limiter==3.12  # Rate limiting
flask-talisman==1.1.0  # Security headers

# Monitoring and logging
structlog==25.3.0  # Structured logging

# Security: Additional hardening packages
werkzeug==3.1.3  # Secure WSGI utilities
itsdangerous==2.2.0  # Secure token generation
click==8.1.8  # Secure CLI framework
markupsafe==3.0.2  # Secure template rendering

# Security: Development and testing
bandit==1.8.0  # Security linter for Python
safety==4.0.1  # Dependency vulnerability scanner
