# Security-hardened dependencies with pinned versions
# Last updated: 2024-05-28

# AI API clients with security patches
anthropic==0.52.0
openai==1.82.0

# Environment and configuration
python-dotenv==1.0.0

# Web framework with security patches
flask==3.0.3
flask-cors==4.0.1

# HTTP client with security patches
httpx==0.27.0

# Async support (built into Python 3.11+)
# asyncio is part of standard library

# Security: Additional dependencies for production
gunicorn==22.0.0  # Production WSGI server
cryptography==42.0.7  # Secure cryptographic operations
certifi==2024.2.2  # Updated CA certificates
urllib3==2.2.1  # Secure HTTP library
requests==2.31.0  # HTTP library with security patches

# Security: Input validation and sanitization
bleach==6.1.0  # HTML sanitization
validators==0.28.1  # Input validation

# Security: Rate limiting and protection
flask-limiter==3.7.0  # Rate limiting
flask-talisman==1.1.0  # Security headers

# Monitoring and logging
structlog==24.1.0  # Structured logging
