# Security: Exclude sensitive files and directories from Docker build context

# Version control
.git/
.gitignore
.gitattributes

# Environment files with secrets (CRITICAL SECURITY)
.env
.env.*
!.env.example
!.env.cloud.example

# <PERSON>rok executables and configs
ngrok*
*.exe

# n8n data (contains workflows and credentials)
n8n-data/

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Testing and coverage
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
htmlcov/
.nox/
test_*.py
setup_*.py

# Logs and temporary files
*.log
logs/
tmp/
temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation (keep only essential)
*.md
!README.md
LICENSE

# Docker files (avoid recursive builds)
Dockerfile*
docker-compose*.yml

# Cloud and deployment files
deploy-cloud.py
start-cloud.sh
CLOUD_DEPLOYMENT_GUIDE.md

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate and key files (CRITICAL SECURITY)
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# SSH keys (CRITICAL SECURITY)
id_rsa*
id_ed25519*
*.pub
.ssh/

# Cloud provider credentials (CRITICAL SECURITY)
.aws/
.azure/
.gcloud/
*.json

# Backup files
*.bak
*.backup
*.old

# Large archive files
*.zip
*.tar.gz
*.rar
*.7z

# Security tools output
.bandit
.safety
.semgrep

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/