#!/usr/bin/env python3
"""
Markdown Linting Fixer
Automatically fixes common markdown linting issues
"""

import re
import os
from pathlib import Path

class MarkdownLintFixer:
    def __init__(self):
        self.fixes_applied = 0
        
    def fix_file(self, file_path):
        """Fix markdown linting issues in a file"""
        print(f"🔧 Fixing {file_path}...")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix MD031: Fenced code blocks should be surrounded by blank lines
        content = self.fix_fenced_code_blocks(content)
        
        # Fix MD032: Lists should be surrounded by blank lines
        content = self.fix_list_spacing(content)
        
        # Fix MD022: Headings should be surrounded by blank lines
        content = self.fix_heading_spacing(content)
        
        # Fix MD026: No trailing punctuation in headings
        content = self.fix_heading_punctuation(content)
        
        # Fix MD040: Fenced code blocks should have a language specified
        content = self.fix_code_block_language(content)
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            self.fixes_applied += 1
        else:
            print(f"ℹ️ No fixes needed for {file_path}")
    
    def fix_fenced_code_blocks(self, content):
        """Fix MD031: Fenced code blocks should be surrounded by blank lines"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Check if this is a code block start
            if line.strip().startswith('```'):
                # Add blank line before if previous line is not blank
                if i > 0 and lines[i-1].strip() != '':
                    if not fixed_lines or fixed_lines[-1].strip() != '':
                        fixed_lines.append('')
            
            fixed_lines.append(line)
            
            # Check if this is a code block end
            if line.strip() == '```' and i < len(lines) - 1:
                # Add blank line after if next line is not blank
                if i < len(lines) - 1 and lines[i+1].strip() != '':
                    fixed_lines.append('')
        
        return '\n'.join(fixed_lines)
    
    def fix_list_spacing(self, content):
        """Fix MD032: Lists should be surrounded by blank lines"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Check if this is a list item
            if re.match(r'^[\s]*[-*+]\s', line) or re.match(r'^[\s]*\d+\.\s', line):
                # Add blank line before list if previous line is not blank and not a list
                if i > 0 and lines[i-1].strip() != '':
                    prev_is_list = re.match(r'^[\s]*[-*+]\s', lines[i-1]) or re.match(r'^[\s]*\d+\.\s', lines[i-1])
                    if not prev_is_list:
                        if not fixed_lines or fixed_lines[-1].strip() != '':
                            fixed_lines.append('')
            
            fixed_lines.append(line)
            
            # Check if this is the end of a list
            if re.match(r'^[\s]*[-*+]\s', line) or re.match(r'^[\s]*\d+\.\s', line):
                if i < len(lines) - 1:
                    next_line = lines[i+1]
                    next_is_list = re.match(r'^[\s]*[-*+]\s', next_line) or re.match(r'^[\s]*\d+\.\s', next_line)
                    if not next_is_list and next_line.strip() != '':
                        fixed_lines.append('')
        
        return '\n'.join(fixed_lines)
    
    def fix_heading_spacing(self, content):
        """Fix MD022: Headings should be surrounded by blank lines"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Check if this is a heading
            if re.match(r'^#{1,6}\s', line):
                # Add blank line before heading if previous line is not blank
                if i > 0 and lines[i-1].strip() != '':
                    if not fixed_lines or fixed_lines[-1].strip() != '':
                        fixed_lines.append('')
            
            fixed_lines.append(line)
            
            # Add blank line after heading if next line is not blank
            if re.match(r'^#{1,6}\s', line):
                if i < len(lines) - 1 and lines[i+1].strip() != '':
                    fixed_lines.append('')
        
        return '\n'.join(fixed_lines)
    
    def fix_heading_punctuation(self, content):
        """Fix MD026: No trailing punctuation in headings"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            if re.match(r'^#{1,6}\s', line):
                # Remove trailing punctuation from headings
                line = re.sub(r'[.!?:]+$', '', line.rstrip()) 
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def fix_code_block_language(self, content):
        """Fix MD040: Fenced code blocks should have a language specified"""
        # Replace ``` with ```text for blocks without language
        content = re.sub(r'^```\s*$', '```text', content, flags=re.MULTILINE)
        return content
    
    def fix_all_markdown_files(self):
        """Fix all markdown files in the current directory"""
        print("🔍 Finding markdown files...")
        
        md_files = [
            'README.md',
            'CLOUD_DEPLOYMENT_GUIDE.md', 
            'DOCKER_SECURITY_GUIDE.md',
            'COMMIT_SECURITY_CHECKLIST.md',
            'FREE_DEVELOPMENT_GUIDE.md',
            'MCP_INTEGRATION_GUIDE.md',
            'N8N_MOCK_INTEGRATION.md'
        ]
        
        existing_files = [f for f in md_files if Path(f).exists()]
        
        print(f"📝 Found {len(existing_files)} markdown files to fix")
        
        for file_path in existing_files:
            self.fix_file(file_path)
        
        print(f"\n🎉 Markdown linting fixes complete!")
        print(f"✅ Fixed {self.fixes_applied} files")
        print(f"📊 Total files processed: {len(existing_files)}")

def main():
    """Main function"""
    print("🛠️ Markdown Linting Auto-Fixer")
    print("=" * 40)
    
    fixer = MarkdownLintFixer()
    
    try:
        fixer.fix_all_markdown_files()
        
        print(f"\n💡 RECOMMENDATIONS:")
        print("   1. Review the changes made")
        print("   2. Run markdown linter to verify fixes")
        print("   3. Commit the cleaned markdown files")
        print("   4. Set up automated linting in CI/CD")
        
    except Exception as e:
        print(f"\n❌ Error during fixing: {e}")

if __name__ == "__main__":
    main()
