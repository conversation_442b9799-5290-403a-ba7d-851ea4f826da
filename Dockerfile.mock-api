# Multi-stage build for secure production image
# Use specific version with security patches
FROM python:3.11.9-slim-bookworm as builder

# Security: Set labels for container identification
LABEL maintainer="Instagram Automation Team"
LABEL version="1.0.0"
LABEL description="Secure Mock API Server for Instagram Automation"

# Security: Create non-root user early
RUN groupadd -r -g 1000 appuser && \
    useradd -r -u 1000 -g appuser -m -d /home/<USER>/bin/bash appuser

# Set working directory
WORKDIR /app

# Security: Update packages and install only necessary dependencies
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        gcc=4:12.2.0-3 \
        libc6-dev=2.36-9+deb12u4 \
        curl=7.88.1-10+deb12u5 \
        ca-certificates=20230311 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Security: Copy requirements first for better caching and validation
COPY --chown=appuser:appuser requirements.txt .

# Security: Upgrade pip and install dependencies with hash verification
RUN python -m pip install --no-cache-dir --upgrade pip==24.0 setuptools==69.5.1 wheel==0.43.0 && \
    pip install --no-cache-dir --user --no-warn-script-location -r requirements.txt

# Production stage - minimal secure image
FROM python:3.11.9-slim-bookworm

# Security: Set labels
LABEL maintainer="Instagram Automation Team"
LABEL version="1.0.0"
LABEL description="Secure Mock API Server for Instagram Automation"

# Security: Create non-root user with specific UID/GID
RUN groupadd -r -g 1000 appuser && \
    useradd -r -u 1000 -g appuser -m -d /home/<USER>/bin/bash appuser

# Security: Update base image and install minimal runtime dependencies
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        curl=7.88.1-10+deb12u5 \
        ca-certificates=20230311 \
        tini=0.19.0-1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Set working directory
WORKDIR /app

# Security: Copy Python packages from builder with proper ownership
COPY --from=builder --chown=appuser:appuser /home/<USER>/.local /home/<USER>/.local

# Security: Copy application code with proper ownership and permissions
COPY --chown=appuser:appuser --chmod=644 mock_api_server.py .
COPY --chown=appuser:appuser --chmod=644 mcp_integration.py .
COPY --chown=appuser:appuser --chmod=644 mock_ai_generator.py .
COPY --chown=appuser:appuser --chmod=644 templates/ ./templates/

# Security: Create data directory with proper permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R appuser:appuser /app && \
    chmod 755 /app/data /app/logs

# Security: Remove any potential SUID/SGID binaries
RUN find / -type f \( -perm -4000 -o -perm -2000 \) -exec rm -f {} \; 2>/dev/null || true

# Security: Switch to non-root user
USER appuser

# Security: Set secure PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Security: Set secure environment variables
ENV FLASK_APP=mock_api_server.py \
    FLASK_ENV=production \
    PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Security: Set file creation mask
RUN umask 0027

# Security: Health check with timeout and proper error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f --max-time 5 http://localhost:8000/health || exit 1

# Security: Expose port (non-privileged)
EXPOSE 8000

# Security: Use tini as init system to handle signals properly
ENTRYPOINT ["tini", "--"]

# Security: Run application with proper signal handling
CMD ["python", "-u", "mock_api_server.py"]
