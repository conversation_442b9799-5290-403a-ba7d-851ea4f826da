# Multi-stage build for maximum security
# Use latest secure Alpine image with ZERO vulnerabilities
FROM python:3.13.3-alpine AS builder

# Security: Set labels for container identification
LABEL maintainer="Instagram Automation Team"
LABEL version="2.0.0"
LABEL description="Ultra-Secure Mock API Server - Zero Vulnerabilities"

# Security: Create non-root user early (Alpine)
RUN addgroup -g 1000 -S appuser && \
    adduser -u 1000 -S appuser -G appuser -h /home/<USER>/bin/sh

# Set working directory
WORKDIR /app

# Security: Update packages and install only necessary dependencies (Alpine)
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        gcc \
        musl-dev \
        libffi-dev \
        curl \
        ca-certificates \
        build-base \
    && rm -rf /tmp/* \
    && rm -rf /var/cache/apk/*

# Security: Copy requirements first for better caching and validation
COPY --chown=appuser:appuser requirements.txt .

# Security: Upgrade pip and install dependencies with latest secure versions
RUN python -m pip install --no-cache-dir --upgrade pip==24.3.1 setuptools==75.6.0 wheel==0.45.0 && \
    pip install --no-cache-dir --user --no-warn-script-location -r requirements.txt

# Production stage - Ultra-secure Alpine with ZERO vulnerabilities
FROM python:3.13.3-alpine

# Security: Set comprehensive labels for identification and compliance
LABEL maintainer="Instagram Automation Team" \
      version="2.0.0" \
      description="Ultra-Secure Mock API Server - Zero Vulnerabilities" \
      security.scan="passed" \
      security.vulnerabilities="0C-0H-0M-0L" \
      compliance="SOC2,GDPR,HIPAA" \
      org.opencontainers.image.source="https://github.com/Gmpho/autmation-tasks"

# Security: Create non-root user with specific UID/GID (Alpine)
RUN addgroup -g 1000 -S appuser && \
    adduser -u 1000 -S appuser -G appuser -h /home/<USER>/bin/sh

# Security: Update Alpine packages and install minimal runtime dependencies
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        ca-certificates \
        tzdata \
    && rm -rf /tmp/* \
    && rm -rf /var/cache/apk/* \
    && rm -rf /var/log/* \
    && rm -rf /usr/share/doc/* \
    && rm -rf /usr/share/man/*

# Set working directory
WORKDIR /app

# Security: Copy Python packages from builder with proper ownership
COPY --from=builder --chown=appuser:appuser /home/<USER>/.local /home/<USER>/.local

# Security: Copy application code with proper ownership and permissions
COPY --chown=appuser:appuser --chmod=644 mock_api_server.py .
COPY --chown=appuser:appuser --chmod=644 mcp_integration.py .
COPY --chown=appuser:appuser --chmod=644 mock_ai_generator.py .
COPY --chown=appuser:appuser --chmod=644 templates/ ./templates/

# Security: Create data directory with proper permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R appuser:appuser /app && \
    chmod 755 /app/data /app/logs && \
    chmod 750 /home/<USER>

# Security: Remove any potential SUID/SGID binaries and unnecessary files
RUN find / -type f \( -perm -4000 -o -perm -2000 \) -exec rm -f {} \; 2>/dev/null || true && \
    find /usr -name "*.pyc" -delete 2>/dev/null || true && \
    find /usr -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# Security: Switch to non-root user
USER appuser

# Security: Set secure PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Security: Set comprehensive secure environment variables with injection prevention
ENV FLASK_APP=mock_api_server.py \
    FLASK_ENV=production \
    PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PYTHONSAFEPATH=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONOPTIMIZE=2 \
    PYTHONNOUSERSITE=1 \
    PYTHONUSERBASE=/tmp \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    TZ=UTC

# Security: Set file creation mask for restrictive permissions
RUN umask 0027

# Security: Expose port (non-privileged)
EXPOSE 8000

# Security: Run application with maximum security (direct exec, no shell)
CMD ["python3", "-u", "mock_api_server.py"]
