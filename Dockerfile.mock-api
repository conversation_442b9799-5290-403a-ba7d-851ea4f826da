# Multi-stage build for secure production image
# Use Alpine-based image for minimal attack surface
FROM python:3.12-alpine3.20 AS builder

# Security: Set labels for container identification
LABEL maintainer="Instagram Automation Team"
LABEL version="1.0.0"
LABEL description="Secure Mock API Server for Instagram Automation"

# Security: Create non-root user early (Alpine)
RUN addgroup -g 1000 -S appuser && \
    adduser -u 1000 -S appuser -G appuser -h /home/<USER>/bin/sh

# Set working directory
WORKDIR /app

# Security: Update packages and install only necessary dependencies (Alpine)
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        gcc \
        musl-dev \
        libffi-dev \
        curl \
        ca-certificates \
        build-base \
    && rm -rf /tmp/* \
    && rm -rf /var/cache/apk/*

# Security: Copy requirements first for better caching and validation
COPY --chown=appuser:appuser requirements.txt .

# Security: Upgrade pip and install dependencies with latest secure versions
RUN python -m pip install --no-cache-dir --upgrade pip==24.3.1 setuptools==75.6.0 wheel==0.45.0 && \
    pip install --no-cache-dir --user --no-warn-script-location -r requirements.txt

# Production stage - minimal secure Alpine image
FROM python:3.12-alpine3.20

# Security: Set labels
LABEL maintainer="Instagram Automation Team"
LABEL version="1.0.0"
LABEL description="Secure Mock API Server for Instagram Automation"

# Security: Create non-root user with specific UID/GID (Alpine)
RUN addgroup -g 1000 -S appuser && \
    adduser -u 1000 -S appuser -G appuser -h /home/<USER>/bin/sh

# Security: Update Alpine packages and install minimal runtime dependencies
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        curl \
        ca-certificates \
        tini \
        dumb-init \
    && rm -rf /tmp/* \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Security: Copy Python packages from builder with proper ownership
COPY --from=builder --chown=appuser:appuser /home/<USER>/.local /home/<USER>/.local

# Security: Copy application code with proper ownership and permissions
COPY --chown=appuser:appuser --chmod=644 mock_api_server.py .
COPY --chown=appuser:appuser --chmod=644 mcp_integration.py .
COPY --chown=appuser:appuser --chmod=644 mock_ai_generator.py .
COPY --chown=appuser:appuser --chmod=644 templates/ ./templates/

# Security: Create data directory with proper permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R appuser:appuser /app && \
    chmod 755 /app/data /app/logs

# Security: Remove any potential SUID/SGID binaries
RUN find / -type f \( -perm -4000 -o -perm -2000 \) -exec rm -f {} \; 2>/dev/null || true

# Security: Switch to non-root user
USER appuser

# Security: Set secure PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Security: Set secure environment variables
ENV FLASK_APP=mock_api_server.py \
    FLASK_ENV=production \
    PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Security: Set file creation mask
RUN umask 0027

# Security: Health check with timeout and proper error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f --max-time 5 http://localhost:8000/health || exit 1

# Security: Expose port (non-privileged)
EXPOSE 8000

# Security: Use tini as init system to handle signals properly
ENTRYPOINT ["tini", "--"]

# Security: Run application with proper signal handling
CMD ["python", "-u", "mock_api_server.py"]
