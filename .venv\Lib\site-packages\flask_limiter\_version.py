
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-05-19T08:52:51-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "cef6e473fa852c69316841ec020f6e334456ecc7",
 "version": "3.7.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
